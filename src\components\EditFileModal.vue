<template>
  <a-modal
    v-model:open="visible"
    title="编辑文件"
    :width="600"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      @finish="onFinish"
    >
      <!-- 文件名称 -->
      <a-form-item
        label="文件名称"
        name="fileName"
        :required="true"
      >
        <a-input
          v-model:value="formData.fileName"
          placeholder="请输入文件名称"
          size="large"
          :style="{ borderRadius: '8px' }"
        />
      </a-form-item>

      <!-- 开启状态 -->
      <a-form-item
        label="开启状态"
        :required="true"
      >
        <a-switch
          v-model:checked="formData.isEnabled"
          size="default"
          :style="{ backgroundColor: formData.isEnabled ? '#1890ff' : '#00000040' }"
        />
      </a-form-item>

      <!-- 标签 -->
      <a-form-item
        label="标签"
        :required="true"
      >
        <div class="tags-container">
          <a-tag
            v-for="(tag, index) in formData.tags"
            :key="index"
            closable
            @close="removeTag(index)"
            class="tag-item"
            color="default"
          >
            {{ tag }}
          </a-tag>
          <a-input
            v-if="inputVisible"
            ref="inputRef"
            v-model:value="inputValue"
            type="text"
            size="small"
            :style="{ width: '100px', borderRadius: '4px' }"
            @blur="handleInputConfirm"
            @keyup.enter="handleInputConfirm"
            class="tag-input"
          />
          <a-button
            v-else
            @click="showInput"
            class="add-tag-btn"
            type="dashed"
            size="small"
          >
            <plus-outlined />
            新标签
          </a-button>
        </div>
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'

// Props
interface Props {
  open?: boolean
  fileData?: {
    fileName?: string
    isEnabled?: boolean
    tags?: string[]
  }
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
  fileData: () => ({
    fileName: '',
    isEnabled: true,
    tags: []
  })
})

// Emits
const emit = defineEmits<{
  'update:open': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = ref(props.open)
const formRef = ref<FormInstance>()
const inputRef = ref()
const inputVisible = ref(false)
const inputValue = ref('')

// 表单数据
const formData = reactive({
  fileName: '',
  isEnabled: true,
  tags: [] as string[]
})

// 表单验证规则
const rules = {
  fileName: [
    { required: true, message: '请输入文件名称', trigger: 'blur' }
  ]
}

// 监听 props 变化
watch(() => props.open, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 重置表单数据
    formData.fileName = props.fileData?.fileName || ''
    formData.isEnabled = props.fileData?.isEnabled ?? true
    formData.tags = [...(props.fileData?.tags || [])]
  }
})

watch(visible, (newVal) => {
  emit('update:open', newVal)
})

// 添加标签相关方法
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !formData.tags.includes(inputValue.value)) {
    formData.tags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const removeTag = (index: number) => {
  formData.tags.splice(index, 1)
}

// 表单提交
const onFinish = (values: any) => {
  console.log('表单数据:', values)
  emit('confirm', {
    fileName: formData.fileName,
    isEnabled: formData.isEnabled,
    tags: formData.tags
  })
  visible.value = false
  message.success('文件编辑成功！')
}

// 确定按钮
const handleOk = () => {
  formRef.value?.validateFields().then(() => {
    onFinish(formData)
  }).catch((error) => {
    console.log('表单验证失败:', error)
  })
}

// 取消按钮
const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.tag-item {
  margin: 0;
  border-radius: 4px;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  color: #333;
  padding: 4px 8px;
  font-size: 14px;
}

.add-tag-btn {
  margin: 0;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
  background: transparent;
  color: #666;
  height: 28px;
  padding: 0 8px;
  font-size: 14px;
}

.add-tag-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.tag-input {
  margin: 0;
  height: 28px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
  font-size: 14px;
}

:deep(.ant-form-item-label > label::before) {
  color: #ff4d4f;
}

:deep(.ant-input) {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  font-size: 14px;
}

:deep(.ant-input:focus) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

:deep(.ant-switch) {
  background-color: #1890ff;
  border-radius: 12px;
}

:deep(.ant-switch:not(.ant-switch-checked)) {
  background-color: rgba(0, 0, 0, 0.25);
}

:deep(.ant-switch-handle) {
  width: 18px;
  height: 18px;
  top: 1px;
}

:deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

:deep(.ant-modal-title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.ant-modal-body) {
  padding: 24px;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #f0f0f0;
  padding: 10px 16px;
  text-align: right;
}
</style>
